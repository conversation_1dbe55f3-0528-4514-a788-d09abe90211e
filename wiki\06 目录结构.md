## 目录结构

```
+---app
|   +---Console
|   |   |   Kernel.php
|   |   \---Commands //Artisan 命令
|   |           CloseUnpiadOrder.php //关闭超时未支付订单
|   |           GenerateSitemap.php //创建站点地图
|   |           InitialInstalltion.php //初始化安装
|   |           InstallPublic.php //安装产品测试图
|   +---Events
|   |       CreatedOrder.php //成功创建订单事件
|   |       OrderClosed.php //订单关闭事件
|   |       OrderShipped.php //订单配送事件
|   +---Exceptions
|   +---Helpers
|   +---Http
|   |   |   Kernel.php
|   |   +---Controllers
|   |   |   |   ArticleController.php
|   |   |   |   Controller.php
|   |   |   |   HomeController.php
|   |   |   |   
|   |   |   +---Strongadmin //后台管理
|   |   |   |   +---Article //文章管理
|   |   |   |   +---Product //产品管理
|   |   |   |   +---Region //地区管理
|   |   |   |   \---User //会员管理
|   |   |   |        
|   |   |   +---Auth //登录、注册等
|   |   |   +---Common //公共
|   |   |   +---Payment //支付
|   |   |   +---Product //产品
|   |   |   \---User //个人中心
|   |   |           
|   |   \---Middleware
|   |       |   Authenticate.php //登录认证中间件
|   |       +---Admin //后台中间件
|   |       |       Auth.php //登录认证中间件
|   |       |       CheckPermission.php //权限检测中间件
|   |       |       Language.php //默认语言设置中间件
|   |       |       Log.php //日志记录中间件
|   |       |       
|   |       +---Common //公共中间件
|   |       |       Language.php //默认语言设置中间件
|   |       |       Uuid.php //uuid 设置中间件
|   |       |       
|   |       \---Web //web组中间件
|   |               SourceRecord.php //访问来源设置中间件
|   |               ViewShare.php //视图共享变量中间件
|   |               
|   +---Jobs //队列任务
|   |       SendPushNotification.php //发送用户通知 队列任务
|   |       
|   +---Listeners //监听器
|   |   |   UpdateShoppingCart.php //更新购物车信息
|   |   |   
|   |   +---Order
|   |   |       IncrementProductSaleNum.php //累加销量
|   |   |       OrderSourceRecord.php //订单来源记录
|   |   |       SendOrderClosedNotification.php //发送`订单关闭`通知
|   |   |       SendOrderShippedNotification.php //发送`订单配送`通知
|   |   |       
|   |   \---User
|   |           SendRegisteredNotification.php //发送`注册成功`通知
|   |           UserSourceRecord.php //用户注册来源记录
|   |           
|   +---Mail
|   |       FeedbackReply.php //反馈回复邮件模板
|   |       Promotional.php //促销邮件模板
|   |       
|   +---Models //模型
|   +---Notifications
|   |   +---Order
|   |   |       OrderClosedNotification.php //订单关闭通知
|   |   |       OrderShippedNotification.php //订单配送通知
|   |   \---User
|   |           LoginSuccess.php //登录成功通知
|   |           RegisteredSuccess.php //注册成功通知
|   |           SendSecurityCodeNotification.php //验证码通知
|   +---Providers
|   +---Repositories //可复用的业务代码都写在这里(注:不是真正意义上的 Repository 模式)
|   |   |   AppRepository.php //app基础核心
|   |   |   AuthRepository.php //登录认证
|   |   |   CartRepository.php //购物车
|   |   |   OrderRepository.php //订单
|   |   |   ProductRepository.php //产品
|   |   |   RegionRepository.php //地区
|   |   |   ShippingRepository.php //配送方式
|   |   |   
|   |   +---Admin
|   |   |       ProductRepositories.php
|   |   |       
|   |   \---Traits
|   |           AppTrait.php //注入到 App\Http\Controllers\Controller 使用
|   |           
|   \---Rules
|           CheckFileType.php //验证规则 - 检测文件类型
|           NotExists.php //验证规则 - 与 exists 相反
|           
+---bootstrap
|   |   app.php
|   |   
|   \---cache
|           .gitignore
|           
+---config
|       app.php
|       backup.php //数据备份配置
|       strongshop.php //strongshop 站点配置
|       telescope.php
|       view.php
|       
+---database
+---resources
|   +---lang //语言翻译
|   \---views
|       +---strongadmin //后台管理视图
|       \---themes
|           \---default //前台默认视图
+---routes
|       admin.php //后台路由
|       api.php
|       channels.php
|       console.php
|       web.php //前台 web 路由
|       
+---storage
|   +---app
|   +---framework
|   +---install
|   \---logs
|  
|   .env.example //配置文件示例
|   artisan //artisan 命令
|   import.sql //完整数据库文件
            
```