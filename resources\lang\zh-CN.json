{"A fresh verification link has been sent to your email address.": "新的验证链接已发送到您的 E-mail。", "All rights reserved.": "版本所有。", "Before proceeding, please check your email for a verification link.": "在继续之前请先验证您的 E-mail。", "click here to request another": "点击重新发送 E-mail", "Confirm Password": "确认密码", "E-Mail Address": "E-mail", "Forbidden": "访问被拒绝", "Forgot Your Password?": "忘记密码？", "Go Home": "回首页", "Hello!": "您好：", "If you did not create an account, no further action is required.": "如果您未注册帐号，请忽略此邮件。", "If you did not receive the email": "如果您没有收到", "If you did not request a password reset, no further action is required.": "如果您未申请重设密码，请忽略此邮件。", "If you’re having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "如果您点击「:actionText」按钮时出现问题，请复制下方链接到浏览器中开启：", "Invalid signature.": "签名无效", "Login": "登录", "Logout": "注销", "Name": "姓名", "Not Found": "页面不存在", "Oh no": "不好了", "Page Expired": "页面会话已超时", "Password": "密码", "Please click the button below to verify your email address.": "请点击下面按钮验证您的 E-mail：", "Please confirm your password before continuing.": "如要继续操作，请先确认密码。", "Regards": "致敬", "Register": "注册", "Remember Me": "记住我", "Reset Password": "重设密码", "Reset Password Notification": "重设密码通知", "Send Password Reset Link": "发送重设密码链接", "Server Error": "服务器错误", "Service Unavailable": "暂时不提供服务", "This action is unauthorized.": "权限不足", "This password reset link will expire in :count minutes.": "这个重设密码链接将会在 :count 分钟后失效。", "Toggle navigation": "切换导航", "Too Many Attempts.": "尝试次数过多", "Too Many Requests": "请求次数过多", "Unauthorized": "未授权", "Verify Email Address": "验证 E-mail", "Verify Your Email Address": "验证 E-mail", "We won't ask for your password again for a few hours.": "确认完成后，接下来几个小时内您不需再输入密码。", "You are receiving this email because we received a password reset request for your account.": "您收到此电子邮件是因为我们收到了您帐户的密码重设请求。", "Your email address is not verified.": "您的电子邮件尚未验证通过", "Whoops!": "哎呦！", "Registration Successful Notification": "注册成功通知", "View Official Website": "访问官网", "You are receiving this email because you regegistered successfully.": "您收到此电子邮件是因为您已经注册成功。", "Search Products": "搜索产品", "Email": "邮箱", "Sign in": "登录", "Sign up": "注册", "Sign out": "退出", "Create New Customer Account": "创建新账号", "Personal Information": "个人信息", "Sign in Information": "登录信息", "I will sign in.": "去登陆.", "We need your email to send you your order confirmation and tracking details. We will never pass your email to third parties.": "我们需要您的电子邮件向您发送订单确认和跟踪详细信息。 我们绝不会将您的电子邮件传递给第三方。", "Submit": "提交", "Slogan": "开源，免费", "Home": "首页", "Shopping Cart": "购物车", "Wish List": "愿望清单", "My Account": "我的账号", "Account": "账号", "All Categories": "所有分类", "Categories": "分类", "About Us": "关于我们", "Contact Us": "联系我们", "Feedback Us": "意见反馈", "My Feedaback": "我的反馈", "Submit New Feedback": "提交新的反馈", "Recommend Products": "推荐产品", "New Products": "新产品", "Hot Products": "热卖产品", "clicks": "次点击", "views": "次查看", "Item code #": "产品编号", "In Stock.": "有库存.", "Stock Out.": "缺货.", "Share to": "分享", "QTY": "数量", "Wholesale": "批发", "Price": "价格", "ADD TO CART": "加入购物车", "Buy Now": "立即购买", "Add to wish list": "加入愿望清单", "Details": "详情", "Reviews": "评论", "Comment Content": "评论内容", "Previous": "上页", "Next": "下页", "Sort By": "排序", "Filter": "筛选", "Lowest Price": "价格由低到高", "Highest Price": "价格由高到低", "Best Views": "点击量", "Best Selling": "销量", "Stock Status": "库存状态", "Subscribe": "订阅", "Customer Support": "客户支持", "Help & Advice": "帮助&建议", "Job": "招聘", "Services": "服务", "Site Map": "站点地图", "Shopping with us": "购物", "Delivery": "配送", "Returns": "退货", "Privacy & Security": "隐私&安全", "Connect with us": "与我们联系", "Sign Up for Our Newsletter": "邮件订阅", "No data.": "暂无数据", "My Orders": "我的订单", "My Wish List": "我的收藏", "My Address": "我的地址", "My Feedback": "我的反馈", "User Home": "用户主页", "Update": "更新", "Birthday": "生日", "Gender": "性别", "Secrecy": "保密", "Male": "男", "Female": "女", "Credits": "积分", "Use Credits": "使用积分", "You have <b>:have_credits</b> credits, The maximum credits for the order that you can use are <b>:viable_credits</b> credits.": "您有 <b>:have_credits</b> 积分, 本次消费您可以使用 <b>:viable_credits</b> 积分.", "About Credits?": "关于积分?", "Welcome back to :shop！": "欢迎回到 :shop！", "Registered Customers": "已注册会员", "New Customers": "新会员", "Cart Subtotal": "购物车小计", "Go back to add more": "返回继续购买", "Shipping Cost": "运费", "Proceed to checkout": "去 结 算", "Address": "地址", "Shipping Address": "配送地址", "Billing Address": "账单地址", "Country": "国家", "State/Province": "省", "Other State/Province": "其他省", "Town/City": "城市", "Address Line 1": "地址 1", "Address Line 2": "地址 2", "First Name": "名", "Last Name": "姓", "Email Address": "邮件地址", "Phone Number": "电话号码", "Fax Number": "传真电话", "Zip/Postal Code": "邮编", "Billing this address": "账单使用此配送地址", "Shipping Options": "配送方式", "Payment Options": "支付方式", "[0,1] Item total(:count item)|[2,99999]Item total(:count items)": "产品小计(:count 件)", "Handing Cost": "支付手续费", "Tax Cost": "税费", "Order Total": "订单总计", "Order Remark": "订单备注", "PLACE MY ORDER": "创建订单", "Order NO.": "订单编号", "Products": "产品", "Product Categories": "产品分类", "Products Amount": "产品金额", "Order Amount": "订单金额", "Created At": "创建时间", "Paid At": "支付时间", "Canceled At": "取消时间", "Received At": "收货时间", "Order Tracking": "订单追踪", "Tracking Number": "物流跟踪单号", "Shipping": "配送", "Payment": "支付", "Shipping To": "配送到", "Unpaid": "待支付", "Paid": "已支付", "Pay Exception": "支付异常", "Pay Failed": "支付失败", "Shipped": "已发货", "Received": "已收货", "Returning": "退货中", "Returned": "已退货", "Done": "已完成", "Canceled": "已取消", "Closed": "已关闭", "Your email address is not verified. It is strongly recommended that you verify the email": "您的邮箱地址还未验证，强烈建议您完成邮箱地址验证，这样您可以找回密码和接收发货通知邮件。<button type=\"button\" class=\"btn btn-link p-0 m-0 align-baseline\" onclick=\"Util.resendEmailVerify();\">发送验证邮箱</button>", "Sign in with email security code.": "使用验证码登录", "Get Verification Code": "获取验证码", "Verification Code": "验证码", "Sign in with password.": "使用密码登录", "Email not validated": "邮箱还未验证", "Resend validation email": "重新发送验证邮件", "You will have many benefits after creating a new account.": "创建一个新账号后您将拥有很多权益。", "Please sign in": "请登录", "Product specification": "产品参数", "Captcha": "验证码"}