- 进入项目目录
```
cd strongshop
composer install
```

- 创建数据库，名称为 `strongshop`
> 确保数据库默认引擎是 `InnoDB`，否则执行`数据库迁移`时可能会报错。

- 导入完整数据库文件
导入文件 /import.sql 到数据库 strongshop

- 在配置文件中`.env` 配置数据库地址和账号
```
DB_DATABASE=数据库名称
DB_USERNAME=数据库账号
DB_PASSWORD=数据库密码
```
> 如果没有 `.env` 文件，可以 复制 `.env.example` 并重命名为 `.env`

- 使用 Artisan 命令 `key:generate` 创建应用密钥
```
php artisan key:generate --ansi
```

- 使用 Artisan 命令 `install:public` 来安装产品图片
```
php artisan strongshop:install:public
```

- 使用 Artisan 命令 `storage:link` 来创建符号链接：参考 https://learnku.com/docs/laravel/6.x/filesystem/5163#69e36e
```
php artisan storage:link
```