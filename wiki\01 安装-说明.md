> Gitee 仓库地址：<a href="https://gitee.com/openstrong/strongshop" target="_blank">https://gitee.com/openstrong/strongshop</a>

### 啰嗦一句
该项目在没有对 laravel 基础框架进行改写的情况下充分使用了 laravel 的 中间件、事件系统、artisan 命令行、模型关联 等特性，
这使得项目架构有着更好的解耦性，也更易于上手和二次开发,所有此项目目前比较适合中小型项目敏捷二开。
后期该项目可能会考虑对 laravel 基础框架进行些许改写，以便更适用于中大型项目和插件开发使用。

### 架构简要
- `请求入口文件`：public/index.php， 所有的请求都是经由你的 Web 服务器（Apache/Nginx）通过配置引导到这个文件。

- `路由`：routes/web.php， 用于定义 web（前台主页面） 界面的路由。routes/admin.php 文件用于定义 admin（后台管理） 界面的路由。

- `中间件`：app/Http/Middleware， 中间件提供了一种方便的机制来过滤进入应用程序的 HTTP 请求。登录认证、权限检测、日志记录功能都写在这里。

- `控制器`：app/Http/Controllers，控制器能将相关的请求处理逻辑组成一个单独的类，大部分业务功能都写在这里。

- `Eloquent 模型`：app/Models，每个数据库表都有一个对应的「模型」用来与该表交互。此项目主要用到 模型关联、访问器、属性类型转换。

- `视图`：resources/views，视图包含应用程序的 HTML 服务，并且将控制器 / 应用程序逻辑与演示逻辑分开。

- `Repositories`：app/Repositories，可复用的业务代码都写在这里(注:不是真正意义上的 Repository 模式)
