> Tip

> 对象存储支持使用 阿里云oss、七牛云 oss
> 请先确保开通 阿里云 oss 或者 七牛云 oss 服务。

在项目更目录下找到配置文件 /.env

- 配置阿里云 oss
```
OSS_DRIVER=oss # 固定为 oss，如果不设置则表示禁用
OSS_ACCESS_KEY={ACCESS_KEY} # oss access_key
OSS_SECRET_KEY={SECRET_KEY} # oss secret_key
OSS_ENDPOINT={ENDPOINT} # 地域节点，例 https://oss-cn-qingdao.aliyuncs.com
OSS_BUCKET={BUCKET} # BUCKET 存储桶, 例 strongshop
```

- 配置 七牛云 oss
```
QIUNIU_DRIVER=qiniu # 固定为 qiniu，如果不设置则表示禁用
QIUNIU_ACCESS_KEY={ACCESS_KEY} # qiniu access_key
QIUNIU_SECRET_KEY={SECRET_KEY} # qiniu secret_key
QIUNIU_BUCKET={BUCKET} # BUCKET 存储桶, 例 strongshop
QINIU_DOMAIN={DOMAIN} #域名，例 strongshop.hn-bkt.clouddn.com
```

- 配置 oss 自定义访问域名，这里以 阿里云 oss 为例：
1. 阿里云控制台 -- 对象存储oss -- bucket列表 -- strongshop -- 传输管理 -- 绑定域名(例：oss.strongshop.cn)
> Tip
> 
> 如果想使用 https 请自行上传并绑定证书

2. 接下来请按照阿里云提示配置
3. 在配置文件 .env 添加：
```
ASSET_URL=http://oss.strongshop.cn
```