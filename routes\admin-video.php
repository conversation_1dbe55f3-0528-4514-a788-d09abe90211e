<?php
use Illuminate\Support\Facades\Route;
use App\Services\OssService;

Route::post('/strongadmin/upload/video', function(){
    $file = request()->file('file');
    
    // 验证文件
    if (!$file->isValid()) {
        return response()->json(['code' => 1, 'msg' => '文件上传失败']);
    }
    if (!in_array($file->extension(), ['mp4'])) {
        return response()->json(['code' => 2, 'msg' => '仅支持MP4格式']);
    }

    try {
        // 上传到OSS
        $ossPath = 'videos/'.date('Ym').'/'.$file->hashName();
        $ossUrl = (new OssService())->upload($ossPath, $file->getRealPath());
        
        return response()->json([
            'code' => 0,
            'data' => ['url' => $ossUrl]
        ]);
    } catch (\Exception $e) {
        return response()->json(['code' => 500, 'msg' => '服务器错误: '.$e->getMessage()]);
    }
});