<?php
namespace App\Libraries;
use OSS\OssClient;

class Oss {
    public static function upload($file) {
        $client = new OssClient(
            env('OSS_ACCESS_KEY_ID'),
            env('OSS_ACCESS_KEY_SECRET'),
            env('OSS_ENDPOINT')
        );
        $path = 'uploads/'.date('Ym').'/'.md5(time()).'.'.$file->extension();
        $client->uploadFile(env('OSS_BUCKET'), $path, $file->getRealPath());
        return $path;
    }
}