<?php
namespace App\Services;

use OSS\OssClient;
use OSS\Core\OssException;

class OssService {
    private $client;
    
    public function __construct() {
        $this->client = new OssClient(
            env('OSS_ACCESS_KEY_ID'),
            env('OSS_ACCESS_KEY_SECRET'),
            env('OSS_ENDPOINT')
        );
    }

public function upload($objectName, $localFilePath) {
    try {
        $this->client->uploadFile(env('OSS_BUCKET'), $objectName, $localFilePath);
        
        // 返回永久访问链接（无需签名）
        return 'https://' . env('OSS_BUCKET') . '.' . env('OSS_ENDPOINT') . '/' . $objectName;
        
    } catch (OssException $e) {
        throw new \Exception("上传失败: ".$e->getMessage());
    }
}

    private function getTempUrl($objectName, $expire = 36000000000) {
        return $this->client->signUrl(env('OSS_BUCKET'), $objectName, $expire);
    }
}

