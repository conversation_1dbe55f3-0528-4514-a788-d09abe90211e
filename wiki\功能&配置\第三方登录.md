Facebook登录和 Google登录要求客户端和服务器端可以访问 Facebook、Google 站点
> 建议：
> 1.服务器端使用国外服务器
> 2.客户端能使用代理软件正常访问 Facebook、Google 站点

下面以 https://demo.strongshop.cn 站点为例

#### Facebook 登录
https://developers.facebook.com/apps/

- 创建应用
类型：消费者；显示名：strongshop

- 设置-基本
 - 配置应用域名：demo.strongshop.cn
 - 获取 CLIENT_ID 和 CLIENT_SECRET

- Facebook 登录-设置
有效 OAuth 跳转 URI：https://demo.strongshop.cn/auth/facebook/callback

- 配置 .env
```
FACEBOOK_CLIENT_ID=CLIENT_ID
FACEBOOK_CLIENT_SECRET=CLIENT_SECRET
FACEBOOK_REDIRECT=${APP_URL}/auth/facebook/callback
```

#### Google 登录
https://console.developers.google.com/apis/dashboard

- 新建项目
项目名称：strongshop

- OAuth 同意屏幕
 - 应用名称：strongshop
 - 应用网域：https://demo.strongshop.cn
 - 已授权的网域：strongshop.cn

- 凭据-创建凭据(OAuth 客户端 ID)
 - 应用类型：Web 应用
 - 名称：Web 客户端 1
 - 已获授权的 JavaScript 来源：https://demo.strongshop.cn
 - 已获授权的重定向 URI：https://demo.strongshop.cn/auth/google/callback

- 配置 .env
```
GOOGLE_CLIENT_ID=CLIENT_ID
GOOGLE_CLIENT_SECRET=CLIENT_SECRET
GOOGLE_REDIRECT=${APP_URL}/auth/google/callback
```