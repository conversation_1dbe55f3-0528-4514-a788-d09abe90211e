<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMp4ToProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('st_product', function (Blueprint $table) {
            $table->string('mp4')->nullable()->after('img_photos')->comment('产品视频链接');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('st_product', function (Blueprint $table) {
            $table->dropColumn('mp4');
        });
    }
}
