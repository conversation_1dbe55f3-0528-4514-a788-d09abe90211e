> Tip
> 用户的密码找回、重置都依赖邮件验证。

- 使用 smtp 驱动

    请先确保邮箱开通 smtp 发信服务，然后再进行以下配置
    这里以 QQ邮箱为例

    1. 开通 smtp
    <img width="900" src="/images/Snipaste_2022-03-31_10-59-30.jpg" />

    2. smtp端口号获取
    请参考：https://service.mail.qq.com/cgi-bin/help?subtype=1&&id=28&&no=369

    3. 后台配置
    <img width="600" src="/images/Snipaste_2021-11-23_13-39-38.jpg" />


----------------------------------


- 使用 mailgun 驱动

    具体参考：https://learnku.com/docs/laravel/6.x/mail/5165#0dfefc

    1. 安装 邮件驱动预备知识
    
    ```
    composer require guzzlehttp/guzzle
    ```
    1. 在配置文件 `.env` 中设置 `MAIL_MAILER` 为 `mailgun`。
    ```
    MAIL_MAILER=mailgun
    ```
    接下来，验证配置文件 config/services.php 包含如下选项：
    ```
    'mailgun' => [
        'domain' => 'your-mailgun-domain',
        'secret' => 'your-mailgun-key',
    ],
    ```