> Gitee 仓库地址：<a href="https://gitee.com/openstrong/strongshop" target="_blank">https://gitee.com/openstrong/strongshop</a>

> 这里以 CentOS7 为例，安装宝塔环境：<a href="http://www.bt.cn" target="_blank">http://www.bt.cn</a>

> Web环境要求： `MySql 5.7+`,`Nginx 1.10+`,`PHP >= 7.2.5`

> 安装 php 扩展 `fileinfo`；解除禁用函数 `symlink`，`putenv`，`proc_open`

- 进入 wwwroot 目录
```
cd /www/wwwroot
```

- 下载项目
    - 通过 Git 克隆
    ```
    #安装 git
    yum install git
    #安装 compsoer
    yum install composer
    #设置 composer 使用阿里云 composer 镜像
    composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
    #克隆项目
    git clone https://gitee.com/openstrong/strongshop.git
    #进入项目目录
    cd strongshop
    #安装 composer 包
    composer install
    ```
    > Tip
    > composer 包安装完毕后，检查 strongshop/vendor 目录，如果存在说明包安装成功，否则项目无法运行
    
    - 下载完整安装包（如果无法通过git完成下载和安装，可以使用此方式）
    <a href="/download" target="_blank">点击下载</a>

    - 修改项目目录权限和所属用户组
```
chmod 777 -R /www/wwwroot/strongshop/bootstrap
chmod 777 -R /www/wwwroot/strongshop/storage
```

- 创建网站站点：www.strongshop.local
 <img src="/images/install03.png" />
> Tip
> 请记得 取消勾选 `防跨站攻击(open_basedir)`，否则访问站点可能会报错。
> 保存运行目录为 /public

- 配置伪静态 （必须配置，否则无法访问）

    - Nginx 伪静态
    ```
    location / {
            try_files $uri $uri/ /index.php?$query_string;
    }
    ```

    - Apache 伪静态
    ```
        Options +FollowSymLinks -Indexes
        RewriteEngine On

        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^ index.php [L]
    ```
- 进入安装页面 http://www.strongshop.local
 <hr/>
 <img style="max-width:500px;" src="/images/install01.jpg" />

> Tip
> 如果无法安装成功，请尝试 <a href="/wiki/installHand">手动安装</a>

- 安装成功
1. 访问站点首页 http://www.strongshop.local
2. 访问站点后台 http://www.strongshop.local/admin/home <br>
超级管理员：`admin` 密码：`123456`

- 配置计划任务（建议一定要配置此项，否则会产生日志爆满问题）
请查看  文档 - 功能&配置 - 计划任务


