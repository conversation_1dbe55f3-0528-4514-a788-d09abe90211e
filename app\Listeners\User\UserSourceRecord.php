<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Listeners\User;

use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UserSourceRecord
{

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  Registered  $event
     * @return void
     */
    public function handle(Registered $event)
    {
        $http_referer = session()->get('http_referer');
        $source = session()->get('source');
        if ($http_referer)
        {
            $event->user->http_referer = $http_referer;
        }
        if ($source)
        {
            $event->user->source = $source;
        }
        $event->user->save();
    }

}
