<!DOCTYPE html>
<html>
<head>
    <title>OSS上传工具</title>
    <style>
        .uploader { margin: 20px; padding: 20px; border: 1px solid #ddd; }
        .progress { height: 20px; background: #eee; margin: 10px 0; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
    </style>
	<script src="node.js"></script>
</head>
<body>
    <div class="uploader">
        <input type="file" id="fileInput">
        <button onclick="uploadFile()">上传文件</button>
        <div class="progress">
            <div class="progress-bar" style="width: 0%"></div>
        </div>
        <div id="result"></div>
    </div>

    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.17.0.min.js"></script>
    <script>
        let ossClient = null;

        async function initOSSClient() {
            const response = await fetch('/sts-token');
            const credentials = await response.json();
            
            ossClient = new OSS({
                region: credentials.region,
                accessKeyId: credentials.accessKeyId,
                accessKeySecret: credentials.accessKeySecret,
                stsToken: credentials.stsToken,
                bucket: credentials.bucket,
                refreshSTSToken: async () => {
                    const res = await fetch('/sts-token');
                    return res.json();
                }
            });
        }

        async function uploadFile() {
            const file = document.getElementById('fileInput').files[0];
            if (!file) return;

            await initOSSClient();
            
            // 生成带日期的存储路径
            const date = new Date();
            const filePath = `uploads/${date.getFullYear()}/${date.getMonth()+1}/${date.getDate()}/${file.name}`;

            try {
                const result = await ossClient.multipartUpload(filePath, file, {
                    progress: (p) => {
                        document.querySelector('.progress-bar').style.width = `${Math.round(p * 100)}%`;
                    }
                });

                // 生成永久直链（有效期10年）
                const url = ossClient.signatureUrl(result.name, {
                    expires: 315360000,
                    response: {
                        'content-disposition': 'inline'
                    }
                });

                document.getElementById('result').innerHTML = `
                    <p>上传成功！</p>
                    <p>直链地址：<a href="${url}" target="_blank">${url}</a></p>
                    <input type="text" value="${url}" style="width:80%">
                `;
            } catch (err) {
                console.error(err);
                alert('上传失败：' + err.message);
            }
        }
    </script>
</body>
</html>