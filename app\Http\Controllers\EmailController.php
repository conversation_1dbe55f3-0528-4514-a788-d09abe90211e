<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmailController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|max:255|unique:st_email,email'
        ]);

        DB::table('st_email')->insert([
            'email' => $validated['email'],
            'time' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Submitted successfully!'
        ]);
    }
}