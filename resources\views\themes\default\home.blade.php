@extends('layouts.app')
@push('styles')
<meta http-equiv="pragram" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
<style>
    .carousel-indicators li{
        border: 1px solid gray;
    }
    .carousel-indicators .active{
        border: 1px solid #fff;
        background: gray;
    }
</style>
@endpush
@section('content')
<!-- Banner 轮播图-->
<div class="st-banner">
    <div class="container">
        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <ol class="carousel-indicators">
                <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
                <li data-target="#carousel-example-generic" data-slide-to="1" class=""></li>
            </ol>
            <div class="carousel-inner" role="listbox">
                <div class="item active">
                    <a href="https://www.shiptobuy.com/product-50.html"><img alt="{{config('strongshop.storeName')}}" src="img/banner01.jpg" data-holder-rendered="true"></a>
                </div>
                <div class="item">
                    <a href="#"><img alt="{{config('strongshop.storeName')}}" src="img/banner02.jpg" data-holder-rendered="true"></a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="st-h20"></div>

<div class="st-home-product">
<script>
// 动态插入优化版箭头
document.addEventListener('DOMContentLoaded', function(){
    const carousel = document.getElementById('carousel-example-generic');
    
    // 创建箭头函数
    const createArrow = (side) => {
        const arrow = document.createElement('div');
        arrow.className = `carousel-arrow arrow-${side}`;
        arrow.onclick = () => {
            $(carousel).carousel(side === 'left' ? 'prev' : 'next');
        };
        return arrow;
    };

    // 插入箭头
    [createArrow('left'), createArrow('right')].forEach(arrow => {
        arrow.style.top = `${carousel.offsetHeight / 2}px`; // 动态计算垂直位置
        carousel.appendChild(arrow);
    });

    // 窗口大小变化时重新定位
    window.addEventListener('resize', () => {
        [...document.getElementsByClassName('carousel-arrow')].forEach(arrow => {
            arrow.style.top = `${carousel.offsetHeight / 2}px`;
        });
    });
});
</script>
    <!--推荐产品-->
    @if($recommendRows->isNotEmpty())
    <div class="container">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_recommend'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('Recommend Products')</a></h4>
        </div>
        <div class="row">
            @foreach($recommendRows as $recommendRow)
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}" class="st-thumb">
                        <img alt="{{$recommendRow->title}}" src="{{$recommendRow->img_cover}}" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="{{$recommendRow->title}}"><a href="{{route('product.show.rewrite', ['id'=>$recommendRow->id])}}">{{$recommendRow->title}}</a></h5>
                        <p class="st-home-product-price">@price($recommendRow->sale_price)</p>
                         <script>
    document.addEventListener("DOMContentLoaded", function(){
        document.querySelectorAll('.st-home-product-price').forEach(e => {
            e.textContent = e.textContent.replace(/(\d+\.\d{3})/, m => (+m).toFixed(2));
        });
    });
  </script>
                        <p class="st-home-product-sold">{{$recommendRow->click_num}} @lang('clicks')</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--新品-->
    @if($newRows->isNotEmpty())
    <div class="container">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_new'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('New Products')</a></h4>
        </div>
        <div class="row">
            @foreach($newRows as $newRow)
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}" class="st-thumb">
                        <img alt="{{$newRow->title}}" src="{{$newRow->img_cover}}" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="{{$newRow->title}}"><a href="{{route('product.show.rewrite', ['id'=>$newRow->id])}}">{{$newRow->title}}</a></h5>
                        <p class="st-home-product-price">@price($newRow->sale_price)</p>
                        <p class="st-home-product-sold">{{$newRow->click_num}} @lang('clicks')</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    <!--热卖-->
    @if($hotRows->isNotEmpty())
    <div class="container">
        <div class="page-header">
            <h4><a href="{{route('product.list', ['is_hot'=>1])}}" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> @lang('Hot Products')</a></h4>
        </div>
        <div class="row">
            @foreach($hotRows as $hotRow)
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}" class="st-thumb">
                        <img alt="{{$hotRow->title}}" src="{{$hotRow->img_cover}}" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="{{$hotRow->title}}"><a href="{{route('product.show.rewrite', ['id'=>$hotRow->id])}}">{{$hotRow->title}}</a></h5>
                        <p class="st-home-product-price">@price($hotRow->sale_price)</p>
                        <p class="st-home-product-sold">{{$hotRow->click_num}} @lang('clicks')</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection
